<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Call Management System</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .call-button {
            transition: all 0.2s ease;
        }

        .call-button:hover {
            transform: scale(1.05);
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .incoming-call {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .search-results {
            max-height: 250px;
            overflow-y: auto;
        }

        .chat-panel {
            transition: all 0.3s ease;
        }

        .active-call-item {
            transition: all 0.2s ease;
        }

        .active-call-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top Navigation -->
        <header class="bg-white border-b border-gray-200">
            <div class="flex items-center justify-between px-4 py-3">
                <div class="flex items-center md:hidden">
                    <button type="button" class="text-gray-500 hover:text-gray-600 focus:outline-none focus:text-gray-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
                <div class="flex-1 px-4 md:px-0">
                    <div class="relative max-w-md">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input id="search-input" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 sm:text-sm" placeholder="Search contacts..." type="search">
                    </div>
                </div>
                <div class="flex items-center">
                    <button type="button" class="flex items-center text-gray-500 hover:text-gray-600 focus:outline-none mr-4">
                        <i class="fas fa-bell text-lg"></i>
                        <span class="notification-badge ml-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">2</span>
                    </button>
                    <button id="toggle-chat-btn" type="button" class="flex items-center text-gray-500 hover:text-gray-600 focus:outline-none">
                        <i class="fas fa-comment-alt text-lg"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Search Results (Hidden by default) -->
        <div id="search-results" class="hidden absolute top-16 left-4 md:left-72 right-4 md:right-auto md:w-96 bg-white rounded-md shadow-lg z-10 border border-gray-200 search-results">
            <div class="p-3 border-b border-gray-200">
                <h3 class="text-sm font-medium text-gray-700">Search Results</h3>
            </div>
            <div class="p-2">
                <div class="contact-item flex items-center justify-between p-2 hover:bg-gray-100 rounded-md cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">RJ</span>
                        </div>
                        <div class="ml-2">
                            <p class="text-sm font-medium text-gray-800">Robert Johnson</p>
                            <p class="text-xs text-gray-500">Product Manager</p>
                        </div>
                    </div>
                    <div>
                        <button class="call-button p-1 text-blue-600 hover:text-blue-800" title="Call">
                            <i class="fas fa-phone-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="contact-item flex items-center justify-between p-2 hover:bg-gray-100 rounded-md cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-pink-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">EJ</span>
                        </div>
                        <div class="ml-2">
                            <p class="text-sm font-medium text-gray-800">Emily Jones</p>
                            <p class="text-xs text-gray-500">UX Designer</p>
                        </div>
                    </div>
                    <div>
                        <button class="call-button p-1 text-blue-600 hover:text-blue-800" title="Call">
                            <i class="fas fa-phone-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="contact-item flex items-center justify-between p-2 hover:bg-gray-100 rounded-md cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">DW</span>
                        </div>
                        <div class="ml-2">
                            <p class="text-sm font-medium text-gray-800">David Wilson</p>
                            <p class="text-xs text-gray-500">Software Engineer</p>
                        </div>
                    </div>
                    <div>
                        <button class="call-button p-1 text-blue-600 hover:text-blue-800" title="Call">
                            <i class="fas fa-phone-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-y-auto p-4 bg-gray-50">
            <div class="max-w-7xl mx-auto">
                <!-- Incoming Call Alert -->
                <div id="incoming-call" class="incoming-call mb-6 bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-medium">SB</span>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-800">Sarah Brown</h3>
                                <p class="text-sm text-gray-500">Incoming Call</p>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <button id="decline-call" class="call-button flex items-center justify-center w-12 h-12 bg-red-100 text-red-600 rounded-full hover:bg-red-200 focus:outline-none" aria-label="Decline Call">
                                <i class="fas fa-phone-slash"></i>
                            </button>
                            <button id="accept-call" class="call-button flex items-center justify-center w-12 h-12 bg-green-100 text-green-600 rounded-full hover:bg-green-200 focus:outline-none" aria-label="Accept Call">
                                <i class="fas fa-phone-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Active Call Panel -->
                <div id="active-call" class="hidden mb-6 bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex flex-col md:flex-row md:items-center justify-between">
                            <div class="flex items-center mb-4 md:mb-0">
                                <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xl font-medium">SB</span>
                                </div>
                                <div class="ml-4">
                                    <h2 class="text-xl font-semibold text-gray-800">Sarah Brown</h2>
                                    <p class="text-gray-500">00:00:12</p>
                                </div>
                            </div>
                            <div class="flex space-x-3">
                                <button id="transfer-call" class="call-button flex flex-col items-center justify-center p-2 rounded-md hover:bg-gray-100" aria-label="Transfer Call">
                                    <i class="fas fa-random text-gray-600 text-xl mb-1"></i>
                                    <span class="text-xs text-gray-600">Transfer</span>
                                </button>
                                <button id="hold-call" class="call-button flex flex-col items-center justify-center p-2 rounded-md hover:bg-gray-100" aria-label="Hold Call">
                                    <i class="fas fa-pause text-gray-600 text-xl mb-1"></i>
                                    <span class="text-xs text-gray-600">Hold</span>
                                </button>
                                <button id="mute-call" class="call-button flex flex-col items-center justify-center p-2 rounded-md hover:bg-gray-100" aria-label="Mute Call">
                                    <i class="fas fa-microphone-slash text-gray-600 text-xl mb-1"></i>
                                    <span class="text-xs text-gray-600">Mute</span>
                                </button>
                                <button id="end-call" class="call-button flex flex-col items-center justify-center p-2 rounded-md hover:bg-red-100" aria-label="End Call">
                                    <i class="fas fa-phone-slash text-red-600 text-xl mb-1"></i>
                                    <span class="text-xs text-red-600">End</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Directory -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-800">User Directory</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium">JD</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-800">John Doe</p>
                                            <p class="text-xs text-gray-500">Software Developer</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="call-button p-2 text-blue-600 hover:text-blue-800" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium">AS</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-800">Alice Smith</p>
                                            <p class="text-xs text-gray-500">Project Manager</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="call-button p-2 text-blue-600 hover:text-blue-800" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium">RJ</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-800">Robert Johnson</p>
                                            <p class="text-xs text-gray-500">UX Designer</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="call-button p-2 text-blue-600 hover:text-blue-800" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-yellow-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium">EJ</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-800">Emily Jones</p>
                                            <p class="text-xs text-gray-500">Marketing Specialist</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="call-button p-2 text-blue-600 hover:text-blue-800" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium">DW</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-800">David Wilson</p>
                                            <p class="text-xs text-gray-500">Sales Representative</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="call-button p-2 text-blue-600 hover:text-blue-800" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-medium">SB</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-800">Sarah Brown</p>
                                            <p class="text-xs text-gray-500">HR Manager</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="call-button p-2 text-blue-600 hover:text-blue-800" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Chat Panel (Hidden by default) -->
    <div id="chat-panel" class="hidden w-80 bg-white border-l border-gray-200 flex flex-col chat-panel">
        <div class="p-4 border-b border-gray-200 flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-800">Chat</h2>
            <button id="close-chat-btn" class="text-gray-500 hover:text-gray-600 focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="flex-1 overflow-y-auto p-4">
            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex-shrink-0 flex items-center justify-center">
                        <span class="text-white text-sm font-medium">JD</span>
                    </div>
                    <div class="ml-2 bg-gray-100 rounded-lg p-3 max-w-xs">
                        <p class="text-sm text-gray-800">Hi there! Can we discuss the project timeline?</p>
                        <p class="text-xs text-gray-500 mt-1">10:30 AM</p>
                    </div>
                </div>
                <div class="flex items-start justify-end">
                    <div class="mr-2 bg-blue-100 rounded-lg p-3 max-w-xs">
                        <p class="text-sm text-gray-800">Sure, I'm available now. What's your concern?</p>
                        <p class="text-xs text-gray-500 mt-1">10:32 AM</p>
                    </div>
                    <div class="w-8 h-8 bg-green-600 rounded-full flex-shrink-0 flex items-center justify-center">
                        <span class="text-white text-sm font-medium">ME</span>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex-shrink-0 flex items-center justify-center">
                        <span class="text-white text-sm font-medium">JD</span>
                    </div>
                    <div class="ml-2 bg-gray-100 rounded-lg p-3 max-w-xs">
                        <p class="text-sm text-gray-800">I think we need to extend the deadline by a week.</p>
                        <p class="text-xs text-gray-500 mt-1">10:35 AM</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-4 border-t border-gray-200">
            <div class="flex items-center">
                <input type="text" class="flex-1 border border-gray-300 rounded-l-md py-2 px-3 text-sm placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" placeholder="Type a message...">
                <button class="bg-blue-600 text-white rounded-r-md px-3 py-2 text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle search results
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');
    
    searchInput.addEventListener('focus', () => {
        searchResults.classList.remove('hidden');
    });
    
    document.addEventListener('click', (e) => {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });
    
    // Toggle chat panel
    const toggleChatBtn = document.getElementById('toggle-chat-btn');
    const closeChatBtn = document.getElementById('close-chat-btn');
    const chatPanel = document.getElementById('chat-panel');
    
    toggleChatBtn.addEventListener('click', () => {
        chatPanel.classList.toggle('hidden');
    });
    
    closeChatBtn.addEventListener('click', () => {
        chatPanel.classList.add('hidden');
    });
    
    // Handle incoming call
    const acceptCallBtn = document.getElementById('accept-call');
    const declineCallBtn = document.getElementById('decline-call');
    const incomingCall = document.getElementById('incoming-call');
    const activeCall = document.getElementById('active-call');
    
    acceptCallBtn.addEventListener('click', () => {
        incomingCall.classList.add('hidden');
        activeCall.classList.remove('hidden');
    });
    
    declineCallBtn.addEventListener('click', () => {
        incomingCall.classList.add('hidden');
    });
    
    // Handle active call controls
    const endCallBtn = document.getElementById('end-call');
    const muteCallBtn = document.getElementById('mute-call');
    const holdCallBtn = document.getElementById('hold-call');
    
    endCallBtn.addEventListener('click', () => {
        activeCall.classList.add('hidden');
    });
    
    muteCallBtn.addEventListener('click', () => {
        muteCallBtn.classList.toggle('bg-gray-100');
        
        if (muteCallBtn.querySelector('i').classList.contains('fa-microphone-slash')) {
            muteCallBtn.querySelector('i').classList.replace('fa-microphone-slash', 'fa-microphone');
            muteCallBtn.querySelector('span').textContent = 'Unmute';
        } else {
            muteCallBtn.querySelector('i').classList.replace('fa-microphone', 'fa-microphone-slash');
            muteCallBtn.querySelector('span').textContent = 'Mute';
        }
    });
    
    holdCallBtn.addEventListener('click', () => {
        holdCallBtn.classList.toggle('bg-gray-100');
        
        if (holdCallBtn.querySelector('i').classList.contains('fa-pause')) {
            holdCallBtn.querySelector('i').classList.replace('fa-pause', 'fa-play');
            holdCallBtn.querySelector('span').textContent = 'Resume';
        } else {
            holdCallBtn.querySelector('i').classList.replace('fa-play', 'fa-pause');
            holdCallBtn.querySelector('span').textContent = 'Hold';
        }
    });
</script>
</body>
</html>